import asyncio
from typing import AsyncGenerator, List, Dict, Any
from app.models.conversation import AgentType

class AgentService:
    """智能体服务类，处理不同类型的AI对话"""

    def __init__(self):
        # 暂时使用模拟模式进行演示
        self.use_mock = True

    def _get_system_prompt(self, agent_type: AgentType) -> str:
        """根据智能体类型获取系统提示词"""
        prompts = {
            AgentType.CUSTOMER_SERVICE: """
你是一个专业的智能客服助手。你的任务是：
1. 友好、耐心地回答用户的问题
2. 提供准确的产品和服务信息
3. 协助用户解决问题
4. 在必要时引导用户联系人工客服
5. 保持专业和礼貌的语调

请始终以用户为中心，提供有帮助的回答。
            """,

            AgentType.TEXT2SQL: """
你是一个专业的SQL查询助手。你的任务是：
1. 理解用户的自然语言查询需求
2. 将自然语言转换为准确的SQL语句
3. 解释SQL语句的含义和执行逻辑
4. 提供查询结果的分析和解读
5. 确保SQL语句的安全性，避免SQL注入

请确保生成的SQL语句语法正确且高效。
            """,

            AgentType.KNOWLEDGE_QA: """
你是一个知识库问答助手。你的任务是：
1. 基于提供的知识库内容回答用户问题
2. 提供准确、相关的信息
3. 当知识库中没有相关信息时，诚实地告知用户
4. 引用具体的知识来源
5. 提供结构化和易于理解的回答

请确保回答的准确性和相关性。
            """,

            AgentType.CONTENT_CREATION: """
你是一个专业的内容创作助手。你的任务是：
1. 根据用户需求创作高质量的文案内容
2. 适应不同的文案类型和风格要求
3. 确保内容的原创性和吸引力
4. 提供多个版本供用户选择
5. 根据反馈优化和调整内容

请发挥创意，创作出优秀的文案作品。
            """
        }

        return prompts.get(agent_type, "你是一个有帮助的AI助手。")

    async def stream_chat(
        self,
        message: str,
        agent_type: AgentType,
        message_history: List[Dict[str, str]] = None
    ) -> AsyncGenerator[str, None]:
        """流式聊天方法"""
        try:
            # 使用模拟流式响应进行演示
            async for chunk in self._mock_stream_response(message, agent_type):
                yield chunk
        except Exception as e:
            yield f"抱歉，处理您的请求时出现了错误: {str(e)}"

    async def _mock_stream_response(self, message: str, agent_type: AgentType) -> AsyncGenerator[str, None]:
        """模拟流式响应"""
        import asyncio

        responses = {
            AgentType.CUSTOMER_SERVICE: f"感谢您的咨询！关于您提到的「{message}」，我很乐意为您提供帮助。作为智能客服，我可以协助您解决各种问题。请告诉我更多详细信息，我会尽力为您提供最佳解决方案。",
            AgentType.TEXT2SQL: f"我理解您想要查询「{message}」相关的数据。让我为您生成相应的SQL语句：\n\n```sql\nSELECT * FROM table_name WHERE condition LIKE '%{message}%';\n```\n\n这个查询将帮助您获取相关数据。您还需要其他查询优化建议吗？",
            AgentType.KNOWLEDGE_QA: f"根据知识库搜索，关于「{message}」的信息如下：\n\n这是一个很好的问题。基于我的知识库，我可以为您提供以下相关信息和解答。如果您需要更详细的信息，请告诉我具体的方面。",
            AgentType.CONTENT_CREATION: f"很棒的创作需求！关于「{message}」，我可以为您创作以下内容：\n\n让我为您量身定制一份优质的文案。请告诉我您希望的风格、目标受众和具体要求，我会为您创作出色的内容。"
        }

        response_text = responses.get(agent_type, f"您好！关于「{message}」，我正在为您处理这个请求...")

        # 模拟逐字输出
        words = response_text.split()
        for i, word in enumerate(words):
            if i == 0:
                yield word
            else:
                yield " " + word
            await asyncio.sleep(0.1)  # 模拟打字效果

    async def get_agent_capabilities(self, agent_type: AgentType) -> Dict[str, Any]:
        """获取智能体能力描述"""
        capabilities = {
            AgentType.CUSTOMER_SERVICE: {
                "name": "智能客服",
                "description": "专业的客户服务助手，能够回答产品问题、处理投诉、提供技术支持",
                "features": [
                    "产品咨询",
                    "问题解答",
                    "投诉处理",
                    "技术支持",
                    "业务引导"
                ]
            },
            AgentType.TEXT2SQL: {
                "name": "Text2SQL分析师",
                "description": "将自然语言转换为SQL查询，并提供数据分析结果",
                "features": [
                    "自然语言转SQL",
                    "查询优化建议",
                    "结果解释",
                    "数据可视化建议",
                    "安全性检查"
                ]
            },
            AgentType.KNOWLEDGE_QA: {
                "name": "知识库助手",
                "description": "基于企业知识库提供精准的问答服务",
                "features": [
                    "知识检索",
                    "多模态理解",
                    "上下文关联",
                    "来源引用",
                    "知识图谱"
                ]
            },
            AgentType.CONTENT_CREATION: {
                "name": "内容创作师",
                "description": "专业的文案创作助手，支持多种文案类型",
                "features": [
                    "营销文案",
                    "技术文档",
                    "社交媒体内容",
                    "邮件模板",
                    "创意写作"
                ]
            }
        }

        return capabilities.get(agent_type, {})
